import { ref, nextTick, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useNuxtApp } from "#app";
import { useAuthStore } from "@/stores/auth";
import { useOrgStore } from "@/stores/org";
import { useTabContext } from "@/composables/useTabContext";
import { useTabSync } from "@/composables/useTabSync";
import { useSessionExpired } from "@/composables/useSessionExpired";

export default function useAuth() {
  const { setOrgId, setStore } = usePermission();
  const $sdk = useNuxtApp().$sdk;
  const router = useRouter();
  const authStore = useAuthStore();
  const orgStore = useOrgStore();

  // Initialize tab sync for cross-tab communication
  const { broadcastEvent, subscribe } = useTabSync();

  // Initialize session expired modal
  const { showSessionExpiredModal } = useSessionExpired();

  // Set up cross-tab logout listener
  const initializeTabSyncListeners = () => {
    if (process.client) {
      // Listen for logout events from other tabs
      subscribe("LOGOUT", (event) => {
        console.log("[Auth] Received logout event from another tab:", event);
        // Show session expired modal instead of immediate logout
        showSessionExpiredModal(3);
      });

      // Listen for login events from other tabs
      subscribe("LOGIN", async (event) => {
        console.log("[Auth] Received login event from another tab:", event);

        try {
          // Get current path using window.location instead of useRoute()
          const currentPath = window.location.pathname;
          console.log("[Auth] Current path:", currentPath);

          // Check if current page is login page or home page, then redirect to org
          if (currentPath === "/login" || currentPath === "/") {
            console.log(
              "[Auth] Redirecting to org due to login from another tab"
            );

            // Use window.location for more reliable navigation
            window.location.href = "/org";
          } else {
            console.log(
              "[Auth] Not redirecting, current path is:",
              currentPath
            );
          }
        } catch (error) {
          console.error("[Auth] Error handling login event:", error);
        }
      });
    }
  };

  const username = ref("");
  const password = ref("");
  const hiddenPassword = ref(false);
  const notify = ref({ type: "", message: "" });
  const errors = ref({ username: "", password: "" });
  const loading = ref(false);
  // Define a configurable redirect path
  const defaultRedirectPath = "/org"; // Replace with your initial default
  const redirectPath = ref(defaultRedirectPath);

  // Initialize listeners when composable is used
  if (process.client) {
    initializeTabSyncListeners();
  }

  const handleSubmit = async (fromOtherTab: boolean = false) => {
    // Reset errors
    //await $nextTick()
    await nextTick();
    errors.value = { username: "", password: "" };

    // Validate form fields
    if (!username.value.trim()) {
      errors.value.username = "Tài khoản không được để trống";
      return;
    }
    if (!password.value.trim()) {
      errors.value.password = "Mật khẩu không được để trống";
      return;
    }

    // Loading state
    loading.value = true;

    try {
      // Login API call
      const response = await $sdk.auth.login({
        username: username.value,
        password: password.value,
      });

      if (response) {
        orgStore.setOrg(response);
        useCookie("dataOrg").value = JSON.stringify(response.orgPositionsMap);
        setToken(response.accessToken);

        // Broadcast login event to other tabs after successful login
        console.log("fromOtherTab", fromOtherTab);
        if (!fromOtherTab) {
          broadcastEvent("LOGIN", { timestamp: Date.now() });
          console.log("[Auth] Login successful, broadcasting to other tabs");
        }

        nextTick();
        console.log(
          "router.currentRoute.value.query",
          router.currentRoute.value.query
        );
        console.log(
          "router.currentRoute.value.query.path",
          router.currentRoute.value.query.path
        );
        if (router.currentRoute.value.query.orgId) {
          const user = {
            id: response.partyId,
            name: response.fullName,
            email: response.email,
            phone: response.phone,
            avatar: response.avatarUrl,
            birthDate: response.birthDate,
            roles:
              response.orgPositionsMap[
                router?.currentRoute?.value?.query?.orgId as any
              ],
          };

          setOrgId(router.currentRoute.value.query.orgId as string);
          setStore(router.currentRoute.value.query.storeId as string);

          await authStore.setUser(user);
          const query = router.currentRoute.value.query;
          const basePath = query.path || "/";

          const params = new URLSearchParams();

          if (query.orgId) params.append("orgId", String(query.orgId));
          if (query.storeId) params.append("storeId", String(query.storeId));
          if (query.orderId) params.append("orderId", String(query.orderId));
          if (query.customerId)
            params.append("customerId", String(query.customerId));

          const url = `${basePath}?${params.toString()}`;

          navigateTo(url);
          // router.push(url);
          return;
        } else {
          router.push(defaultRedirectPath);
        }
        const user = {
          id: response.partyId,
          name: response.fullName,
          email: response.email,
          phone: response.phone,
          avatar: response.avatarUrl,
          birthDate: response.birthDate,
        };
        authStore.setUser(user);
      }
    } catch (error) {
      useNuxtApp().$toast.error("Vui lòng kiểm tra lại tài khoản mật khẩu");

      console.error("Login error:", error);
    } finally {
      loading.value = false;
    }
  };
  const setToken = (token: string) => {
    const cookie = useCookie("token") as Ref<string>;
    cookie.value = token;
    authStore.setToken(token);
    $sdk.setToken(token);
  };

  const logout = async (fromOtherTab: boolean = false) => {
    try {
      // Broadcast logout event to other tabs (only if not triggered by another tab)
      if (!fromOtherTab) {
        broadcastEvent("LOGOUT", { timestamp: Date.now() });
      }

      // Clear cookies
      useCookie("token").value = null;
      useCookie("dataOrg").value = null;
      useCookie("storeId").value = "N/A";
      useCookie("orgId").value = "N/A";

      // Clear tab-isolated context
      const { clearContext } = useTabContext();
      clearContext();

      // Clear localStorage
      localStorage.clear();

      // Clear session storage for current tab
      sessionStorage.clear();

      // Clear auth store
      authStore.setToken(null);
      authStore.setUser(null);

      // Navigate to login page
      await router.push("/login");

      console.log("[Auth] Logout completed", { fromOtherTab });
    } catch (error) {
      console.error("[Auth] Error during logout:", error);
      // Still try to navigate to login even if cleanup fails
      await router.push("/login");
    }
  };

  const togglePasswordVisibility = () => {
    hiddenPassword.value = !hiddenPassword.value;
  };

  // Allow setting the redirect path from outside the composable
  const setRedirectPath = (newPath: string) => {
    redirectPath.value = newPath;
  };
  const checkToken = async (orgId: string, token: string) => {
    try {
      const response = await $sdk.authorization.checkToken(orgId, token);
      return response;
    } catch (error) {
      throw error;
    }
  };
  return {
    username,
    password,
    hiddenPassword,
    notify,
    errors,
    loading,
    handleSubmit,
    togglePasswordVisibility,
    setRedirectPath,
    logout,
    setToken,
    checkToken,
  };
}
