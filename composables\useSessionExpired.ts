/**
 * Session Expired Modal Management Composable
 *
 * This composable manages the session expired modal state
 * and provides methods to show/hide the modal with countdown.
 */

import { ref, readonly } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { useTabContext } from "@/composables/useTabContext";

// Global state for session expired modal
const isSessionExpiredModalVisible = ref(false);
const sessionExpiredCountdown = ref(3);

export const useSessionExpired = () => {
  /**
   * Show session expired modal with countdown
   */
  const showSessionExpiredModal = (countdownSeconds: number = 3) => {
    sessionExpiredCountdown.value = countdownSeconds;
    isSessionExpiredModalVisible.value = true;
    console.log(
      "[SessionExpired] Modal shown with countdown:",
      countdownSeconds
    );
  };

  /**
   * Hide session expired modal
   */
  const hideSessionExpiredModal = () => {
    isSessionExpiredModalVisible.value = false;
    console.log("[SessionExpired] Modal hidden");
  };

  /**
   * Handle when user clicks "Login Now"
   */
  const handleLoginNow = async () => {
    hideSessionExpiredModal();

    // Navigate to login immediately
    const router = useRouter();
    await router.push("/login");

    console.log("[SessionExpired] User chose to login now");
  };

  /**
   * Handle when user clicks "Stay Here" (if allowed)
   */
  const handleStayHere = () => {
    hideSessionExpiredModal();
    console.log("[SessionExpired] User chose to stay");
  };

  /**
   * Handle when countdown expires
   */
  const handleTimeExpired = async () => {
    hideSessionExpiredModal();

    try {
      // Clear cookies
      useCookie("token").value = null;
      useCookie("dataOrg").value = null;
      useCookie("storeId").value = "N/A";
      useCookie("orgId").value = "N/A";

      // Clear tab-isolated context
      const { clearContext } = useTabContext();
      clearContext();

      // Clear localStorage
      localStorage.clear();

      // Clear session storage for current tab
      sessionStorage.clear();

      // Clear auth store
      const authStore = useAuthStore();
      authStore.setToken(null);
      authStore.setUser(null);

      // Navigate to login page
      const router = useRouter();
      await router.push("/login");

      console.log("[SessionExpired] Time expired, auto logout completed");
    } catch (error) {
      console.error("[SessionExpired] Error during auto logout:", error);
      // Still try to navigate to login even if cleanup fails
      const router = useRouter();
      await router.push("/login");
    }
  };

  return {
    // State
    isSessionExpiredModalVisible: readonly(isSessionExpiredModalVisible),
    sessionExpiredCountdown: readonly(sessionExpiredCountdown),

    // Methods
    showSessionExpiredModal,
    hideSessionExpiredModal,
    handleLoginNow,
    handleStayHere,
    handleTimeExpired,
  };
};
