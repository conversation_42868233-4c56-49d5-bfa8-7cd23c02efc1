/**
 * Session Expired Modal Management Composable
 *
 * This composable manages the session expired modal state
 * and provides methods to show/hide the modal with countdown.
 */

import { ref, readonly } from "vue";

// Global state for session expired modal
const isSessionExpiredModalVisible = ref(false);
const sessionExpiredCountdown = ref(3);

export const useSessionExpired = () => {
  /**
   * Show session expired modal with countdown
   */
  const showSessionExpiredModal = (countdownSeconds: number = 3) => {
    sessionExpiredCountdown.value = countdownSeconds;
    isSessionExpiredModalVisible.value = true;
    console.log(
      "[SessionExpired] Modal shown with countdown:",
      countdownSeconds
    );
  };

  /**
   * Hide session expired modal
   */
  const hideSessionExpiredModal = () => {
    isSessionExpiredModalVisible.value = false;
    console.log("[SessionExpired] Modal hidden");
  };

  /**
   * Handle when user clicks "Login Now"
   */
  const handleLoginNow = async () => {
    hideSessionExpiredModal();

    // Navigate to login immediately
    const router = useRouter();
    await router.push("/login");

    console.log("[SessionExpired] User chose to login now");
  };

  /**
   * Handle when user clicks "Stay Here" (if allowed)
   */
  const handleStayHere = () => {
    hideSessionExpiredModal();
    console.log("[SessionExpired] User chose to stay");
  };

  /**
   * Handle when countdown expires
   */
  const handleTimeExpired = async () => {
    hideSessionExpiredModal();

    // Perform logout cleanup and navigate to login
    const { logout } = useAuth();
    await logout(true); // fromOtherTab = true to prevent broadcasting

    console.log("[SessionExpired] Time expired, auto logout");
  };

  return {
    // State
    isSessionExpiredModalVisible: readonly(isSessionExpiredModalVisible),
    sessionExpiredCountdown: readonly(sessionExpiredCountdown),

    // Methods
    showSessionExpiredModal,
    hideSessionExpiredModal,
    handleLoginNow,
    handleStayHere,
    handleTimeExpired,
  };
};
