<template>
  <Transition
    enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="opacity-0 scale-95"
    enter-to-class="opacity-100 scale-100"
    leave-active-class="transition-all duration-200 ease-in"
    leave-from-class="opacity-100 scale-100"
    leave-to-class="opacity-0 scale-95"
  >
    <div
      v-if="isVisible"
      class="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm"
      @click.self="handleBackdropClick"
    >
      <div
        class="relative mx-4 w-full max-w-md rounded-2xl bg-white p-6 shadow-2xl ring-1 ring-gray-200 dark:bg-gray-800 dark:ring-gray-700"
        role="dialog"
        aria-modal="true"
        aria-labelledby="session-expired-title"
        aria-describedby="session-expired-description"
      >
        <!-- Icon -->
        <div
          class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
        >
          <svg
            class="h-8 w-8 text-red-600 dark:text-red-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        <!-- Title -->
        <h3
          id="session-expired-title"
          class="mb-2 text-center text-xl font-semibold text-gray-900 dark:text-white"
        >
          Phiên hoạt động đã hết hạn
        </h3>

        <!-- Description -->
        <p
          id="session-expired-description"
          class="mb-6 text-center text-gray-600 dark:text-gray-300"
        >
          Bạn đã đăng xuất từ tab khác. Hệ thống sẽ tự động chuyển về trang đăng
          nhập.
        </p>

        <!-- Countdown -->
        <div class="mb-6 text-center">
          <div
            class="inline-flex items-center justify-center rounded-full bg-red-50 px-4 py-2 dark:bg-red-900/20"
          >
            <svg
              class="mr-2 h-5 w-5 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span
              class="text-lg font-mono font-semibold text-red-600 dark:text-red-400"
            >
              {{ countdown }}
            </span>
            <span class="ml-1 text-sm text-red-600 dark:text-red-400"
              >giây</span
            >
          </div>
        </div>

        <!-- Actions -->
        <div class="flex flex-col gap-3 sm:flex-row">
          <button
            @click="handleLoginNow"
            class="flex-1 rounded-lg bg-red-600 px-4 py-2.5 text-sm font-medium text-white transition-colors hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            Đăng nhập ngay
          </button>
          <button
            @click="handleStayHere"
            class="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:focus:ring-offset-gray-800"
          >
            Ở lại ({{ countdown }}s)
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from "vue";

// Props
interface Props {
  isVisible?: boolean;
  countdownSeconds?: number;
  allowStayHere?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isVisible: false,
  countdownSeconds: 3,
  allowStayHere: false,
});

// Emits
const emit = defineEmits<{
  loginNow: [];
  stayHere: [];
  timeExpired: [];
  close: [];
}>();

// State
const countdown = ref(props.countdownSeconds);
let countdownInterval: NodeJS.Timeout | null = null;

// Methods
const startCountdown = () => {
  countdown.value = props.countdownSeconds;

  countdownInterval = setInterval(() => {
    countdown.value--;

    if (countdown.value <= 0) {
      stopCountdown();
      emit("timeExpired");
    }
  }, 1000);
};

const stopCountdown = () => {
  if (countdownInterval) {
    clearInterval(countdownInterval);
    countdownInterval = null;
  }
};

const handleLoginNow = () => {
  stopCountdown();
  emit("loginNow");
};

const handleStayHere = () => {
  if (props.allowStayHere) {
    stopCountdown();
    emit("stayHere");
  }
};

const handleBackdropClick = () => {
  // Prevent closing by clicking backdrop during session expiry
  if (!props.allowStayHere) {
    return;
  }
  emit("close");
};

// Watchers
watch(
  () => props.isVisible,
  (newValue) => {
    if (newValue) {
      startCountdown();
    } else {
      stopCountdown();
    }
  }
);

// Lifecycle
onMounted(() => {
  if (props.isVisible) {
    startCountdown();
  }
});

onUnmounted(() => {
  stopCountdown();
});

// Keyboard handling
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.isVisible) return;

  if (event.key === "Enter") {
    handleLoginNow();
  } else if (event.key === "Escape" && props.allowStayHere) {
    handleStayHere();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
/* Additional styles if needed */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}
</style>
