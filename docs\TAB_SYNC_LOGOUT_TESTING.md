# Hướng dẫn Test Tính năng Đồng bộ Logout với Popup Thông báo

## 🎯 Tổng quan

Tính năng đồng bộ logout đã được nâng cấp với popup thông báo "Phiên hoạt động đã hết hạn" khi logout từ tab khác. Khi một tab logout, các tab khác sẽ hiển thị popup với countdown 3 giây trước khi tự động chuyển về trang đăng nhập.

## 🔄 Cách thức hoạt động

### 1. Công nghệ sử dụng
- **BroadcastChannel API**: Cho các trình duyệt hiện đại
- **localStorage Events**: Fallback cho trình duyệt cũ
- **SessionExpiredModal**: Component popup thông báo với countdown
- **Tab-isolated SessionStorage**: Context riêng cho mỗi tab

### 2. Luồng hoạt động mới
1. **Khi user logout ở Tab A**:
   - Tab A broadcast sự kiện `LOGOUT` qua BroadcastChannel
   - Tab A ghi event vào localStorage (fallback)
   - Tab A thực hiện cleanup và chuyển về `/login`

2. **Các tab khác (Tab B, C, D...)**:
   - Nhận được sự kiện `LOGOUT` qua BroadcastChannel hoặc localStorage event
   - **🆕 Hiển thị popup "Phiên hoạt động đã hết hạn"** với countdown 3 giây
   - User có 2 lựa chọn:
     - **"Đăng nhập ngay"**: Chuyển ngay lập tức về `/login`
     - **Chờ countdown**: Sau 3 giây tự động logout và chuyển về `/login`

## 🧪 Hướng dẫn Test

### Bước 1: Chuẩn bị
1. Đảm bảo ứng dụng đang chạy: `yarn dev`
2. Mở trình duyệt và truy cập: `http://localhost:3000`

### Bước 2: Đăng nhập
1. Đăng nhập với tài khoản hợp lệ
2. Đảm bảo bạn đã vào được dashboard hoặc trang chính

### Bước 3: Mở nhiều tab
1. **Tab 1**: Giữ nguyên tab hiện tại (đã đăng nhập)
2. **Tab 2**: Mở tab mới, truy cập `http://localhost:3000` 
   - Tab này sẽ tự động redirect vào dashboard (do đã có token)
3. **Tab 3**: Mở thêm tab thứ 3 tương tự
4. **Tab 4+**: Có thể mở thêm nhiều tab để test

### Bước 4: Test Popup Logout
1. **Ở một trong các tab**, thực hiện logout:
   - Click vào avatar/menu user
   - Click "Đăng xuất"

2. **Quan sát kết quả**:
   - Tab thực hiện logout sẽ chuyển về `/login` ngay lập tức
   - **🎉 Tất cả các tab khác sẽ hiển thị popup thông báo**:
     ```
     ⚠️ Phiên hoạt động đã hết hạn
     Bạn đã đăng xuất từ tab khác. Hệ thống sẽ tự động chuyển về trang đăng nhập.
     
     ⏰ 3 giây
     
     [Đăng nhập ngay] [Ở lại (3s)]
     ```

3. **Test các hành động**:
   - **Để countdown chạy hết**: Popup sẽ tự động đóng và chuyển về login
   - **Click "Đăng nhập ngay"**: Chuyển ngay lập tức về login
   - **Click "Ở lại"**: Không có tác dụng (button bị disable)

### Bước 5: Kiểm tra Console Log
Mở Developer Tools (F12) để xem log:

```javascript
// Log khi khởi tạo
[TabSync Plugin] Initializing tab synchronization...
[TabSync] Initialized with {broadcastChannel: true, localStorage: true}

// Log khi logout ở tab hiện tại
[Auth] Logout completed {fromOtherTab: false}

// Log khi nhận logout từ tab khác
[Auth] Received logout event from another tab: {type: "LOGOUT", timestamp: 1703123456789}
[SessionExpired] Modal shown with countdown: 3

// Log khi user chọn hành động
[SessionExpired] User chose to login now
// hoặc
[SessionExpired] Time expired, auto logout
```

## 🎯 Các trường hợp Test

### Test Case 1: Countdown tự động
- Mở 3 tab, logout ở tab 1
- Ở tab 2 và 3: **Không click gì**, để countdown chạy hết
- **Kết quả**: Sau 3 giây, tất cả tab đều chuyển về login

### Test Case 2: Click "Đăng nhập ngay"
- Mở 3 tab, logout ở tab 1
- Ở tab 2: Click **"Đăng nhập ngay"** ngay khi popup hiện
- **Kết quả**: Tab 2 chuyển ngay về login, tab 3 vẫn countdown

### Test Case 3: Test với nhiều tab (5-10 tab)
- Mở nhiều tab cùng lúc
- Logout ở bất kỳ tab nào
- **Kết quả**: Tất cả tab khác đều hiển thị popup đồng thời

### Test Case 4: Test responsive
- Test trên desktop: Popup hiển thị ở giữa màn hình
- Test trên mobile: Popup responsive, button stack vertical

### Test Case 5: Test keyboard navigation
- Khi popup hiện, nhấn **Enter**: Tương đương click "Đăng nhập ngay"
- Khi popup hiện, nhấn **Escape**: Không có tác dụng (không cho phép đóng)

## 🔧 Tính năng kỹ thuật

### Components được thêm:
- ✅ **SessionExpiredModal.vue**: Component popup với countdown
- ✅ **useSessionExpired.ts**: Composable quản lý trạng thái modal
- ✅ **Tích hợp vào tất cả layouts**: dashboard, custom, customPayment

### Cải tiến UX:
- ✅ **Visual feedback**: Icon cảnh báo, màu đỏ, animation
- ✅ **Countdown timer**: Hiển thị thời gian còn lại rõ ràng
- ✅ **Accessibility**: ARIA labels, keyboard navigation
- ✅ **Responsive**: Hoạt động tốt trên mobile và desktop
- ✅ **Dark mode support**: Tự động adapt theo theme

### Bảo mật:
- ✅ **Không cho phép đóng popup**: User không thể ignore thông báo
- ✅ **Force logout**: Sau countdown sẽ bắt buộc logout
- ✅ **Clean session**: Clear tất cả tokens và session data

## 🐛 Xử lý sự cố

### Nếu popup không hiển thị:
1. **Kiểm tra Console**: Có lỗi JavaScript không?
2. **Kiểm tra BroadcastChannel**: Trình duyệt có hỗ trợ không?
3. **Kiểm tra localStorage**: Events có được fire không?

### Nếu countdown không hoạt động:
1. **Kiểm tra timer**: setInterval có được clear đúng không?
2. **Kiểm tra component lifecycle**: onMounted/onUnmounted
3. **Kiểm tra reactive state**: countdown ref có update không?

### Debug Commands:
```javascript
// Trong console, test manual
const { showSessionExpiredModal } = useSessionExpired();
showSessionExpiredModal(5); // Test với 5 giây

// Kiểm tra trạng thái
localStorage.getItem('tab_sync_event')
sessionStorage.getItem('tabId')
```

## 🎉 Kết luận

Tính năng đồng bộ logout với popup thông báo giúp:
- ✅ **Tăng UX**: User được thông báo rõ ràng về việc logout
- ✅ **Tăng bảo mật**: Không có tab nào còn active sau khi logout
- ✅ **Tăng kiểm soát**: User có thể chọn thời điểm chuyển về login
- ✅ **Professional**: Trải nghiệm nhất quán với các ứng dụng enterprise

**🚀 Test ngay bây giờ tại: http://localhost:3000**
