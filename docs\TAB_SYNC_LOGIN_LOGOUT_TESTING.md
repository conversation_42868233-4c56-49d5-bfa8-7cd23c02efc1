# Hướng dẫn Test Tính năng Đồng bộ Login/Logout giữa các Tab

## 🎯 Tổng quan

Hệ thống đồng bộ tab đã được hoàn thiện với 2 tính năng chính:
1. **Đồng bộ LOGOUT**: Khi logout ở một tab, các tab khác hiển thị popup "Phiên hoạt động đã hết hạn" với countdown 3 giây
2. **🆕 Đồng bộ LOGIN**: <PERSON>hi đăng nhập ở một tab, các tab khác tự động chuyển về trang org/dashboard

## 🔄 Cách thức hoạt động

### 1. C<PERSON>ng nghệ sử dụng
- **BroadcastChannel API**: Cho các trình duyệt hiện đại
- **localStorage Events**: Fallback cho trình duyệt cũ
- **SessionExpiredModal**: Component popup thông báo với countdown
- **Tab-isolated SessionStorage**: Context riêng cho mỗi tab

### 2. Luồng hoạt động LOGOUT
1. **Khi user logout ở Tab A**:
   - Tab A broadcast sự kiện `LOGOUT` qua BroadcastChannel
   - Tab A ghi event vào localStorage (fallback)
   - Tab A thực hiện cleanup và chuyển về `/login`

2. **Các tab khác (Tab B, C, D...)**:
   - Nhận được sự kiện `LOGOUT` qua BroadcastChannel hoặc localStorage event
   - **Hiển thị popup "Phiên hoạt động đã hết hạn"** với countdown 3 giây
   - User có 2 lựa chọn:
     - **"Đăng nhập ngay"**: Chuyển ngay lập tức về `/login`
     - **Chờ countdown**: Sau 3 giây tự động logout và chuyển về `/login`

### 3. 🆕 Luồng hoạt động LOGIN
1. **Khi user login ở Tab A**:
   - Tab A thực hiện đăng nhập thành công
   - Tab A broadcast sự kiện `LOGIN` qua BroadcastChannel
   - Tab A ghi event vào localStorage (fallback)
   - Tab A chuyển về trang org/dashboard

2. **Các tab khác (Tab B, C, D...)**:
   - Nhận được sự kiện `LOGIN` qua BroadcastChannel hoặc localStorage event
   - **Kiểm tra nếu đang ở trang `/login` hoặc `/`**
   - **Tự động chuyển về `/org`** (dashboard)

## 🧪 Hướng dẫn Test

### Bước 1: Chuẩn bị
1. Đảm bảo ứng dụng đang chạy: `yarn dev`
2. Mở trình duyệt và truy cập: `http://localhost:3000`

### 🔐 Test Case A: Đồng bộ LOGIN

#### A1. Test cơ bản
1. **Mở 3 tab** trình duyệt
2. **Tất cả tab** đều sẽ redirect về `/login` (do chưa đăng nhập)
3. **Ở Tab 1**: Đăng nhập với tài khoản hợp lệ
4. **Quan sát kết quả**:
   - Tab 1: Chuyển về `/org` sau khi login thành công
   - **Tab 2 và Tab 3**: Tự động chuyển từ `/login` về `/org`

#### A2. Test với tab ở trang khác
1. **Tab 1**: Ở trang `/login`
2. **Tab 2**: Ở trang `/login` 
3. **Tab 3**: Navigate thủ công về `/` (home page)
4. **Ở Tab 1**: Đăng nhập
5. **Quan sát kết quả**:
   - Tab 1: Chuyển về `/org`
   - Tab 2: Tự động chuyển về `/org` (từ `/login`)
   - Tab 3: Tự động chuyển về `/org` (từ `/`)

#### A3. Test với tab đã ở dashboard
1. **Tab 1**: Ở trang `/login`
2. **Tab 2**: Thủ công navigate về `/org` (sẽ redirect về login do chưa auth)
3. **Ở Tab 1**: Đăng nhập
4. **Quan sát kết quả**:
   - Tab 1: Chuyển về `/org`
   - Tab 2: Không thay đổi (vì không ở `/login` hay `/`)

### 🚪 Test Case B: Đồng bộ LOGOUT

#### B1. Test popup logout
1. **Đăng nhập** và có 3 tab đều ở dashboard
2. **Ở Tab 1**: Thực hiện logout
3. **Quan sát kết quả**:
   - Tab 1: Chuyển ngay về `/login`
   - **Tab 2 và Tab 3**: Hiển thị popup "Phiên hoạt động đã hết hạn"

#### B2. Test countdown tự động
1. Tiếp tục từ B1
2. **Ở Tab 2**: Không click gì, để countdown chạy hết
3. **Quan sát kết quả**:
   - Sau 3 giây: Tab 2 tự động chuyển về `/login`
   - Tab 3: Vẫn hiển thị popup

#### B3. Test "Đăng nhập ngay"
1. Tiếp tục từ B1
2. **Ở Tab 3**: Click button "Đăng nhập ngay"
3. **Quan sát kết quả**:
   - Tab 3: Chuyển ngay lập tức về `/login`

## 🔍 Kiểm tra Console Log

Mở Developer Tools (F12) để xem log:

### Login Events:
```javascript
// Khi login thành công ở tab hiện tại
[Auth] Login successful, broadcasting to other tabs

// Khi nhận login từ tab khác
🔐 [TabSync Plugin] LOGIN event received from another tab: {type: "LOGIN", timestamp: 1703123456789}
[Auth] Received login event from another tab: {type: "LOGIN", timestamp: 1703123456789}
[Auth] Redirecting to org due to login from another tab
```

### Logout Events:
```javascript
// Khi logout ở tab hiện tại
[Auth] Logout completed {fromOtherTab: false}

// Khi nhận logout từ tab khác
🚪 [TabSync Plugin] LOGOUT event received from another tab: {type: "LOGOUT", timestamp: 1703123456789}
[Auth] Received logout event from another tab: {type: "LOGOUT", timestamp: 1703123456789}
[SessionExpired] Modal shown with countdown: 3

// Khi countdown hết
[SessionExpired] Time expired, auto logout completed

// Khi user click "Đăng nhập ngay"
[SessionExpired] User chose to login now
```

## 🎯 Các trường hợp Test nâng cao

### Test Case C: Kết hợp Login + Logout
1. **Mở 4 tab**, tất cả ở `/login`
2. **Tab 1**: Đăng nhập → Tất cả tab chuyển về `/org`
3. **Tab 2**: Logout → Tab 3,4 hiển thị popup
4. **Tab 3**: Click "Đăng nhập ngay" → Chuyển về `/login`
5. **Tab 4**: Để countdown hết → Tự động chuyển về `/login`
6. **Tab 3**: Đăng nhập lại → Tab 4 chuyển về `/org`

### Test Case D: Test với nhiều tab (5-10 tab)
1. **Mở 8 tab** cùng lúc
2. **Login ở tab 1** → 7 tab còn lại chuyển về `/org`
3. **Logout ở tab 5** → 7 tab còn lại hiển thị popup
4. **Quan sát**: Tất cả popup đều hoạt động đồng thời

### Test Case E: Test trên các trình duyệt khác nhau
- **Chrome**: Sử dụng BroadcastChannel
- **Firefox**: Sử dụng BroadcastChannel  
- **Safari**: Sử dụng BroadcastChannel hoặc localStorage fallback
- **Edge**: Sử dụng BroadcastChannel

## 🚀 Kết luận

Hệ thống đồng bộ tab hoàn chỉnh giúp:
- ✅ **Tăng UX**: User không cần login/logout từng tab một
- ✅ **Tăng bảo mật**: Không có tab nào còn active sau khi logout
- ✅ **Tăng tiện lợi**: Login một lần, tất cả tab đều được đăng nhập
- ✅ **Professional**: Trải nghiệm nhất quán với các ứng dụng enterprise

**🚀 Test ngay bây giờ tại: http://localhost:3000**
